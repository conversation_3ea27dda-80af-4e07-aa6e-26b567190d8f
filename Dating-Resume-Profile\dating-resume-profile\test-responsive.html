<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsiveness Test - Dating Resume Profile</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .viewport-test {
            border: 2px solid #333;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        .viewport-header {
            background: #333;
            color: white;
            padding: 10px 20px;
            font-weight: bold;
        }
        .viewport-content {
            height: 600px;
            overflow: auto;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Dating Resume Profile - Responsiveness Test</h1>
        <p>This page shows how the dating resume profile looks at different screen sizes to verify space efficiency.</p>
        
        <div class="test-grid">
            <div class="viewport-test">
                <div class="viewport-header">Desktop View (1024px+)</div>
                <div class="viewport-content" style="width: 1024px;">
                    <iframe src="src/index.html"></iframe>
                </div>
            </div>
            
            <div class="viewport-test">
                <div class="viewport-header">Tablet View (768px)</div>
                <div class="viewport-content" style="width: 768px;">
                    <iframe src="src/index.html"></iframe>
                </div>
            </div>
            
            <div class="viewport-test">
                <div class="viewport-header">Mobile View (480px)</div>
                <div class="viewport-content" style="width: 480px;">
                    <iframe src="src/index.html"></iframe>
                </div>
            </div>
            
            <div class="viewport-test">
                <div class="viewport-header">Small Mobile View (360px)</div>
                <div class="viewport-content" style="width: 360px;">
                    <iframe src="src/index.html"></iframe>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
            <h2>Responsiveness Improvements Made:</h2>
            <ul>
                <li><strong>Fixed HTML Structure:</strong> Moved contact form and details inside proper containers</li>
                <li><strong>Enhanced Mobile Layout:</strong> Improved spacing and sizing for mobile devices</li>
                <li><strong>Optimized Modal:</strong> Better modal sizing and scrolling on small screens</li>
                <li><strong>Improved List Layout:</strong> Better flex layout for skill/hobby items</li>
                <li><strong>Added Breakpoints:</strong> Multiple responsive breakpoints for different screen sizes</li>
                <li><strong>Space Efficiency:</strong> Better utilization of available space across all devices</li>
            </ul>
        </div>
    </div>
</body>
</html>
