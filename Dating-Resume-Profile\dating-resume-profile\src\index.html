<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dating Resume Profile</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- Removed top gradient header section -->
    <div class="container profile-container">
        <div class="profile-header text-center">
            <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Profile Photo" class="profile-photo img-fluid rounded-circle">
            <h1 id="profile-name"><PERSON></h1>
            <p id="profile-tagline">Adventurer | <PERSON>ie | Dog Lover</p>
            <div class="profile-meta">
                <span class="profile-age">Age: 30</span> &nbsp;|&nbsp; <span class="profile-location">New York, NY</span>
            </div>
        </div>
        <div class="profile-details">
            <div class="section-header">
                <h2>Objectives</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="objective" title="Edit Objectives">
                        <i class="edit-icon">✏️</i>
                    </button>
                </div>
            </div>
            <p id="profile-objective" class="editable-text">Seeking a partner-in-crime for spontaneous weekend adventures, cozy nights in debating the best pizza toppings, and building a supportive and laughter-filled life together. Open to a long-term, committed relationship where we both feel seen, valued, and cherished.</p>

            <div class="section-header">
                <h2>Personal Summary</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="summary" title="Edit Personal Summary">
                        <i class="edit-icon">✏️</i>
                    </button>
                </div>
            </div>
            <p id="profile-summary" class="editable-text">An enthusiastic optimist with a passion for live music, a talent for remembering obscure movie quotes, and a firm belief that a good cup of coffee can solve most problems. My friends would describe me as loyal, a great listener, and the one most likely to suggest getting ice cream.</p>

            <div class="section-header">
                <h2>Relationship Qualifications</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="qualifications" title="Edit Qualifications">
                        <i class="edit-icon">✏️</i>
                    </button>
                    <button class="btn-section-add" data-section="qualifications" title="Add Qualification">
                        <i class="add-icon">➕</i>
                    </button>
                    <button class="btn-section-collapse" data-section="qualifications" title="Collapse Section">
                        <i class="collapse-icon">🔽</i>
                    </button>
                </div>
            </div>
            <ul id="profile-qualifications" class="list-group collapsible-section">
                <li class="list-group-item">
                    <span class="item-text">Successfully Navigated a Long-Distance Relationship: Honed communication skills and the art of the creative virtual date night.</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Co-Habitation Veteran: Proficient in the delicate negotiations of shared living spaces and delegating chores.</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Friendship Aficionado: Have cultivated and maintained deep, meaningful friendships that have taught me the value of loyalty, empathy, and showing up for people.</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
            </ul>

            <div class="section-header">
                <h2>Skills</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="skills" title="Edit Skills">
                        <i class="edit-icon">✏️</i>
                    </button>
                    <button class="btn-section-add" data-section="skills" title="Add Skill">
                        <i class="add-icon">➕</i>
                    </button>
                    <button class="btn-section-collapse" data-section="skills" title="Collapse Section">
                        <i class="collapse-icon">🔽</i>
                    </button>
                </div>
            </div>
            <ul id="profile-skills" class="list-group collapsible-section">
                <li class="list-group-item">
                    <span class="item-text">Active Listening & Empathetic Communication</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Conflict Resolution (I fight fair)</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Gifted at offering thoughtful and unsolicited compliments</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Fluent in "Dad Jokes" and Sarcasm</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Expert Spider Remover</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Proficient in assembling IKEA furniture</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Can whip up a mean batch of blueberry pancakes</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Exceptional road trip DJ and snack provider</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
            </ul>

            <div class="section-header">
                <h2>Hobbies</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="hobbies" title="Edit Hobbies">
                        <i class="edit-icon">✏️</i>
                    </button>
                    <button class="btn-section-add" data-section="hobbies" title="Add Hobby">
                        <i class="add-icon">➕</i>
                    </button>
                    <button class="btn-section-collapse" data-section="hobbies" title="Collapse Section">
                        <i class="collapse-icon">🔽</i>
                    </button>
                </div>
            </div>
            <ul id="profile-hobbies" class="list-group collapsible-section">
                <li class="list-group-item">
                    <span class="item-text">Traveling</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Cooking</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Hiking</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Photography</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Landscape photography</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Trying new recipes</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Reading historical documentaries</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Bookstore browsing</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Game nights with friends</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
            </ul>

            <div class="section-header">
                <h2>Personal Growth</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="growth" title="Edit Personal Growth">
                        <i class="edit-icon">✏️</i>
                    </button>
                    <button class="btn-section-add" data-section="growth" title="Add Growth Item">
                        <i class="add-icon">➕</i>
                    </button>
                    <button class="btn-section-collapse" data-section="growth" title="Collapse Section">
                        <i class="collapse-icon">🔽</i>
                    </button>
                </div>
            </div>
            <ul id="profile-growth" class="list-group collapsible-section">
                <li class="list-group-item">
                    <span class="item-text">Currently Reading: "Attached: The New Science of Adult Attachment"</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Recently Mastered: The art of making the perfect Old Fashioned.</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
                <li class="list-group-item">
                    <span class="item-text">Life Lesson Learned: The importance of not taking oneself too seriously.</span>
                    <div class="item-actions">
                        <button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
                        <button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
                    </div>
                </li>
            </ul>

            <div class="section-header">
                <h2>Looking For</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="looking-for" title="Edit Looking For">
                        <i class="edit-icon">✏️</i>
                    </button>
                </div>
            </div>
            <p id="profile-looking-for" class="editable-text">A genuine connection, laughter, and someone who loves dogs.</p>
        </div>

        <!-- Floating Action Button System -->
        <div class="fab-container">
            <button id="fab-main" class="fab fab-main" title="Quick Actions">
                <i class="fab-icon">⚡</i>
            </button>
            <div class="fab-menu" id="fab-menu">
                <button class="fab fab-secondary" id="fab-edit-all" title="Edit All Profile">
                    <i class="fab-icon">✏️</i>
                </button>
                <button class="fab fab-secondary" id="fab-add-skill" title="Add Skill">
                    <i class="fab-icon">🎯</i>
                </button>
                <button class="fab fab-secondary" id="fab-add-hobby" title="Add Hobby">
                    <i class="fab-icon">🎨</i>
                </button>
                <button class="fab fab-secondary" id="fab-contact" title="Show Contact">
                    <i class="fab-icon">📞</i>
                </button>
            </div>
        </div>

        <!-- Primary Action Buttons -->
        <div class="primary-actions">
            <div class="button-group">
                <button id="edit-profile-btn" class="btn btn-primary">
                    <i class="btn-icon">✏️</i> Edit All Profile
                </button>
                <button id="save-profile-btn" class="btn btn-success hidden">
                    <i class="btn-icon">💾</i> Save Changes
                </button>
                <button id="cancel-edit-btn" class="btn btn-secondary hidden">
                    <i class="btn-icon">❌</i> Cancel
                </button>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="contact-section">
            <div class="section-header">
                <h2>Contact Information</h2>
                <div class="section-actions">
                    <button class="btn-section-edit" data-section="contact" title="Edit Contact">
                        <i class="edit-icon">✏️</i>
                    </button>
                </div>
            </div>
            <form id="captcha-form" class="text-center">
                <div class="cf-turnstile" data-sitekey="YOUR_SITE_KEY"></div>
                <button type="submit" class="btn btn-secondary">
                    <i class="btn-icon">👁️</i> Show Contact Details
                </button>
            </form>
            <section id="contact-details" style="display:none;" class="text-center">
                <h2>Contact Details</h2>
                <p>Email: <span id="email"><EMAIL></span></p>
                <p>Phone: <span id="phone">************</span></p>
                <div class="contact-actions">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="btn-icon">📧</i> Email Me
                    </button>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="btn-icon">📱</i> Call Me
                    </button>
                </div>
            </section>
        </div>

        <!-- Quick Add Modals -->
        <div id="quick-add-modal" class="modal hidden">
            <div class="modal-content quick-add-content">
                <span class="close-btn" id="quick-add-close">&times;</span>
                <h3 id="quick-add-title">Add New Item</h3>
                <form id="quick-add-form">
                    <div class="mb-3">
                        <input type="text" id="quick-add-input" class="form-control" placeholder="Enter new item..." required>
                        <input type="hidden" id="quick-add-section">
                    </div>
                    <div class="quick-add-actions">
                        <button type="submit" class="btn btn-primary">Add</button>
                        <button type="button" class="btn btn-secondary" id="quick-add-cancel">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Edit Modal -->
        <div id="quick-edit-modal" class="modal hidden">
            <div class="modal-content quick-edit-content">
                <span class="close-btn" id="quick-edit-close">&times;</span>
                <h3 id="quick-edit-title">Edit Item</h3>
                <form id="quick-edit-form">
                    <div class="mb-3">
                        <textarea id="quick-edit-input" class="form-control" rows="3" required></textarea>
                        <input type="hidden" id="quick-edit-section">
                        <input type="hidden" id="quick-edit-index">
                    </div>
                    <div class="quick-edit-actions">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" id="quick-edit-cancel">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Keyboard Shortcuts Help -->
        <div id="shortcuts-modal" class="modal hidden">
            <div class="modal-content shortcuts-content">
                <span class="close-btn" id="shortcuts-close">&times;</span>
                <h3>⌨️ Keyboard Shortcuts</h3>
                <div class="shortcuts-grid">
                    <div class="shortcut-group">
                        <h4>General</h4>
                        <div class="shortcut-item">
                            <kbd>E</kbd> <span>Edit Profile</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>S</kbd> <span>Save Changes</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Esc</kbd> <span>Close Modal/Cancel</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>?</kbd> <span>Show Shortcuts</span>
                        </div>
                    </div>
                    <div class="shortcut-group">
                        <h4>Quick Actions</h4>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>K</kbd> <span>Add Skill</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>H</kbd> <span>Add Hobby</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl</kbd> + <kbd>Enter</kbd> <span>Quick Save</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F</kbd> <span>Toggle FAB Menu</span>
                        </div>
                    </div>
                    <div class="shortcut-group">
                        <h4>Navigation</h4>
                        <div class="shortcut-item">
                            <kbd>Tab</kbd> <span>Next Element</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Shift</kbd> + <kbd>Tab</kbd> <span>Previous Element</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Space</kbd> <span>Activate Button</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Enter</kbd> <span>Submit Form</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Profile Modal -->
        <div id="edit-profile-modal" class="modal hidden">
            <div class="modal-content">
                <span class="close-btn">&times;</span>
                <h2>Edit Profile</h2>
                <form id="edit-profile-form">
                    <div class="mb-3">
                        <label class="form-label">Name:<br><textarea id="edit-name" class="form-control">Alex Johnson</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tagline:<br><textarea id="edit-tagline" class="form-control">Adventurer | Foodie | Dog Lover</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Objectives:<br><textarea id="edit-objective" class="form-control">Seeking a partner-in-crime for spontaneous weekend adventures, cozy nights in debating the best pizza toppings, and building a supportive and laughter-filled life together. Open to a long-term, committed relationship where we both feel seen, valued, and cherished.</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Relationship Qualifications (comma separated):<br><textarea id="edit-qualifications" class="form-control">Successfully Navigated a Long-Distance Relationship, Co-Habitation Veteran, Friendship Aficionado</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Skills (comma separated):<br><textarea id="edit-skills" class="form-control">Active Listening & Empathetic Communication, Conflict Resolution, Gifted at offering compliments, Fluent in Dad Jokes, Expert Spider Remover, Proficient in assembling IKEA furniture, Can whip up blueberry pancakes, Exceptional road trip DJ</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Hobbies (comma separated):<br><textarea id="edit-hobbies" class="form-control">Traveling, Cooking, Hiking, Photography, Landscape photography, Trying new recipes, Reading historical documentaries, Bookstore browsing, Game nights with friends</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Personal Growth (comma separated):<br><textarea id="edit-growth" class="form-control">Currently Reading: Attached, Recently Mastered: Old Fashioned, Life Lesson Learned: Not taking oneself too seriously</textarea></label><br>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Looking For:<br><textarea id="edit-looking-for" class="form-control">A genuine connection, laughter, and someone who loves dogs.</textarea></label><br>
                    </div>
                    <button type="submit" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container text-center">
            <p>&copy; 2023 Dating Resume Profile</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    <script src="scripts/main.js"></script>
</body>
</html>