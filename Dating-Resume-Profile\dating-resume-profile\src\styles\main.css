#edit-name,
#edit-tagline,
#edit-objective,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth,
#edit-looking-for {
    width: 100%;
    box-sizing: border-box;
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth {
    min-height: 120px;
    resize: vertical;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    width: 100%;
    min-height: 48px;
    box-sizing: border-box;
}
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    min-height: 120px;
    resize: vertical;
}
.modal-content h2 {
    color: #fff;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content input,
.modal-content textarea {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content label {
    color: #fff;
}
.modal-content input,
.modal-content textarea {
    background: #111;
    color: #fff;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
}
#captcha-form button {
    background: #000;
    color: #fff;
    border-radius: 16px;
    font-weight: bold;
    padding: 16px 32px;
    border: none;
    font-size: 1.2em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    cursor: pointer;
    transition: background 0.2s;
}
#captcha-form button:hover {
    background: #222;
}
.modal-content label,
.modal-content,
.modal-content input,
.modal-content textarea {
    color: #fff;
}
#contact-details {
    background: #fff;
    color: #222;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 24px;
    max-width: 350px;
    margin: 32px auto;
}

/* Profile Meta (Age, Location) */
.profile-meta {
    margin-top: 10px;
    font-size: 1.08rem;
    color: #fff;
    font-weight: 500;
    letter-spacing: 0.5px;
}
.profile-age, .profile-location {
    font-style: italic;
}
/* Romantic Resume Info Section Styles */
.romantic-resume-info {
    max-width: 600px;
    margin: 32px auto 0 auto;
    padding: 0 12px;
}
.toggle-info {
    width: 100%;
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    color: #fff;
    border: none;
    border-radius: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 16px 0;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    color: #fff;
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
.toggle-info:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.02);
}
.resume-content {
    margin-top: 0;
    animation: fadeInUp 0.5s cubic-bezier(.39,.575,.565,1.000);
}
.resume-content.hidden {
    display: none;
}
.resume-card {
    background: #000;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(255, 255, 255, 0.1);
    padding: 32px 24px 24px 24px;
    margin-top: 0;
    color: #fff;
}
.resume-card h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
}
.resume-card h3 {
    font-size: 1.2rem;
    margin-top: 18px;
    margin-bottom: 8px;
}
.resume-card h4 {
    font-size: 1.08rem;
    margin-top: 16px;
    margin-bottom: 6px;
}
.resume-card ul {
    margin: 8px 0 16px 0;
    font-size: 1rem;
}
.resume-card li {
    margin-bottom: 6px;
}
.resume-card blockquote {
    background: #181818;
    border-left: 4px solid #fff;
    margin: 10px 0 10px 0;
    padding: 10px 16px;
    font-style: italic;
    border-radius: 6px;
}
/* Add your CSS styles here */

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.profile-container {
    max-width: 800px;
    margin: 48px auto;
        background: #181818;
        border-radius: 18px;
        box-shadow: 0 12px 40px rgba(82,255,3,0.18);
    padding: 40px 32px 32px 32px;
    position: relative;
        color: #fff;
}

@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}

.profile-header {
    text-align: center;
    margin-bottom: 28px;
}

.profile-photo {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 18px;
    border: 5px solid #8ec5fc;
    box-shadow: 0 2px 12px rgba(80,80,160,0.10);
    transition: transform 0.2s;
}
.profile-photo:hover {
    transform: scale(1.05) rotate(-2deg);
}

#profile-name {
    font-size: 2.2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: 1px;
}

#profile-tagline {
    font-size: 1.15rem;
    margin-top: 10px;
    font-style: italic;
}

.profile-details h2 {
    color: #fff;
    margin-top: 28px;
    margin-bottom: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.profile-details p, .profile-details ul {
    color: #fff;
    font-size: 1.07rem;
    margin-bottom: 16px;
    line-height: 1.7;
}

.profile-details ul {
    list-style: disc inside;
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px 20px;
    margin-bottom: 18px;
}
.profile-details ul li {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    border-radius: 18px;
    padding: 8px 18px;
    font-size: 1rem;
    color: #4a3f6b;
    box-shadow: 0 2px 8px rgba(80,80,160,0.09);
    transition: background 0.2s, transform 0.2s;
    font-weight: 500;
}
.profile-details ul li:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.04);
}

#edit-profile-btn {
    display: block;
    margin: 36px auto 0 auto;
    padding: 12px 32px;
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
#edit-profile-btn:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

.modal {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(80,80,160,0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s;
}
.modal.hidden {
    display: none;
}
@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.modal-content {
    background: #fff;
    padding: 36px 28px 28px 28px;
    border-radius: 14px;
    box-shadow: 0 4px 24px rgba(80,80,160,0.18);
    max-width: 540px;
    margin: 56px auto 32px auto;
    background: #232323;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(255,140,0,0.18);
    padding: 48px 36px 36px 36px;
    position: relative;
    animation: fadeInUp 0.8s cubic-bezier(.39,.575,.565,1.000);
    border: 2px solid #fff;
    z-index: 1;
    cursor: pointer;
    transition: color 0.2s;
}
.close-btn:hover {
    color: #e0c3fc;
}
label {
    display: block;
    margin-bottom: 12px;
    color: #4a3f6b;
    font-weight: 500;
}
input[type="text"], textarea {
    width: 100%;
    padding: 10px;
    margin-top: 6px;
    border: 1px solid #8ec5fc;
    border-radius: 6px;
    font-size: 1rem;
    box-sizing: border-box;
    background: #f7f7fa;
    transition: border 0.2s;
}
input[type="text"]:focus, textarea:focus {
    border: 1.5px solid #e0c3fc;
    outline: none;
}
button[type="submit"] {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 28px;
    font-size: 1.05rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 18px;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
button[type="submit"]:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

@media (max-width: 768px) {
    .profile-container {
        margin: 24px auto;
        padding: 24px;
    }

    .profile-details ul {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 12px;
    }

    .profile-container {
        margin: 12px auto;
        padding: 16px;
    }

    .profile-photo {
        width: 100px;
        height: 100px;
    }

    #profile-name {
        font-size: 1.8rem;
    }

    #profile-tagline {
        font-size: 1rem;
    }

    .profile-details h2 {
        font-size: 1.1rem;
    }

    .profile-details p, .profile-details ul {
        font-size: 0.95rem;
    }

    #edit-profile-btn {
        padding: 10px 24px;
    }

    .modal-content {
        padding: 24px;
    }
}
