#edit-name,
#edit-tagline,
#edit-objective,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth,
#edit-looking-for {
    width: 100%;
    box-sizing: border-box;
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for,
#edit-qualifications,
#edit-skills,
#edit-hobbies,
#edit-growth {
    min-height: 120px;
    resize: vertical;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    width: 100%;
    min-height: 48px;
    box-sizing: border-box;
}
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    min-height: 120px;
    resize: vertical;
}
.modal-content h2 {
    color: #fff;
}
.modal-content input,
.modal-content textarea,
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 2px solid #bcd0ee !important;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
#edit-objective,
#edit-looking-for {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content input,
.modal-content textarea {
    background: #111 !important;
    color: #fff !important;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    box-shadow: none;
}
.modal-content label {
    color: #fff;
}
.modal-content input,
.modal-content textarea {
    background: #111;
    color: #fff;
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
}
#captcha-form button {
    background: #000;
    color: #fff;
    border-radius: 16px;
    font-weight: bold;
    padding: 16px 32px;
    border: none;
    font-size: 1.2em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    cursor: pointer;
    transition: background 0.2s;
}
#captcha-form button:hover {
    background: #222;
}
.modal-content label,
.modal-content,
.modal-content input,
.modal-content textarea {
    color: #fff;
}
/* Contact Section Styles */
.contact-section {
    margin-top: 32px;
    padding: 24px 0;
}

#contact-details {
    background: #fff;
    color: #222;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 24px;
    max-width: 350px;
    margin: 32px auto;
}

/* Footer Styles */
.footer {
    background: rgba(0, 0, 0, 0.1);
    color: #fff;
    padding: 20px 0;
    margin-top: 40px;
    text-align: center;
}

.footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Profile Meta (Age, Location) */
.profile-meta {
    margin-top: 10px;
    font-size: 1.08rem;
    color: #fff;
    font-weight: 500;
    letter-spacing: 0.5px;
}
.profile-age, .profile-location {
    font-style: italic;
}
/* Romantic Resume Info Section Styles */
.romantic-resume-info {
    max-width: 600px;
    margin: 32px auto 0 auto;
    padding: 0 12px;
}
.toggle-info {
    width: 100%;
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    color: #fff;
    border: none;
    border-radius: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 16px 0;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    color: #fff;
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
.toggle-info:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.02);
}
.resume-content {
    margin-top: 0;
    animation: fadeInUp 0.5s cubic-bezier(.39,.575,.565,1.000);
}
.resume-content.hidden {
    display: none;
}
.resume-card {
    background: #000;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(255, 255, 255, 0.1);
    padding: 32px 24px 24px 24px;
    margin-top: 0;
    color: #fff;
}
.resume-card h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
}
.resume-card h3 {
    font-size: 1.2rem;
    margin-top: 18px;
    margin-bottom: 8px;
}
.resume-card h4 {
    font-size: 1.08rem;
    margin-top: 16px;
    margin-bottom: 6px;
}
.resume-card ul {
    margin: 8px 0 16px 0;
    font-size: 1rem;
}
.resume-card li {
    margin-bottom: 6px;
}
.resume-card blockquote {
    background: #181818;
    border-left: 4px solid #fff;
    margin: 10px 0 10px 0;
    padding: 10px 16px;
    font-style: italic;
    border-radius: 6px;
}
/* Add your CSS styles here */

/* Global Responsive Utilities */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

.profile-container {
    max-width: 800px;
    margin: 48px auto;
    background: #181818;
    border-radius: 18px;
    box-shadow: 0 12px 40px rgba(82,255,3,0.18);
    padding: 40px 32px 32px 32px;
    position: relative;
    color: #fff;
    width: 95%;
    box-sizing: border-box;
}

@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}

.profile-header {
    text-align: center;
    margin-bottom: 28px;
}

.profile-photo {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 18px;
    border: 5px solid #8ec5fc;
    box-shadow: 0 2px 12px rgba(80,80,160,0.10);
    transition: transform 0.2s;
}
.profile-photo:hover {
    transform: scale(1.05) rotate(-2deg);
}

#profile-name {
    font-size: 2.2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: 1px;
}

#profile-tagline {
    font-size: 1.15rem;
    margin-top: 10px;
    font-style: italic;
}

.profile-details h2 {
    color: #fff;
    margin-top: 28px;
    margin-bottom: 10px;
    font-size: 1.15rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.profile-details p, .profile-details ul {
    color: #fff;
    font-size: 1.07rem;
    margin-bottom: 16px;
    line-height: 1.7;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.profile-details ul {
    list-style: none;
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px 15px;
    margin-bottom: 18px;
    justify-content: flex-start;
    align-items: flex-start;
}
.profile-details ul li {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    border-radius: 18px;
    padding: 8px 16px;
    font-size: 1rem;
    color: #4a3f6b;
    box-shadow: 0 2px 8px rgba(80,80,160,0.09);
    transition: background 0.2s, transform 0.2s;
    font-weight: 500;
    flex: 0 1 auto;
    min-width: fit-content;
    text-align: center;
    word-break: break-word;
    hyphens: auto;
}
.profile-details ul li:hover {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    transform: scale(1.04);
}

#edit-profile-btn {
    display: block;
    margin: 36px auto 0 auto;
    padding: 12px 32px;
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
#edit-profile-btn:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

.modal {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(80,80,160,0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
}
.modal.hidden {
    display: none;
}
@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.modal-content {
    background: #232323;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(255,140,0,0.18);
    padding: 48px 36px 36px 36px;
    position: relative;
    animation: fadeInUp 0.8s cubic-bezier(.39,.575,.565,1.000);
    border: 2px solid #fff;
    z-index: 1;
    transition: color 0.2s;
    max-width: 540px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-sizing: border-box;
}

.close-btn {
    position: absolute;
    top: 16px;
    right: 20px;
    font-size: 24px;
    color: #fff;
    cursor: pointer;
    transition: color 0.2s;
    z-index: 2;
}
.close-btn:hover {
    color: #e0c3fc;
}

/* Ensure close button is always visible and accessible */
@media (max-width: 480px) {
    .close-btn {
        top: 12px;
        right: 16px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
}
label {
    display: block;
    margin-bottom: 12px;
    color: #4a3f6b;
    font-weight: 500;
}
input[type="text"], textarea {
    width: 100%;
    padding: 10px;
    margin-top: 6px;
    border: 1px solid #8ec5fc;
    border-radius: 6px;
    font-size: 1rem;
    box-sizing: border-box;
    background: #f7f7fa;
    transition: border 0.2s;
}
input[type="text"]:focus, textarea:focus {
    border: 1.5px solid #e0c3fc;
    outline: none;
}
button[type="submit"] {
    background: linear-gradient(90deg, #8ec5fc 0%, #e0c3fc 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 28px;
    font-size: 1.05rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 18px;
    box-shadow: 0 2px 8px rgba(80,80,160,0.10);
    transition: background 0.2s, transform 0.2s;
    letter-spacing: 0.5px;
}
button[type="submit"]:hover {
    background: linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%);
    transform: scale(1.04);
}

/* Large Tablet and Small Desktop Styles */
@media (max-width: 1024px) {
    .profile-container {
        max-width: 85%;
        margin: 40px auto;
        padding: 36px 28px;
    }

    .modal-content {
        max-width: 85%;
        margin: 20px;
        padding: 40px 28px;
    }

    .profile-details ul li {
        font-size: 0.98rem;
        padding: 7px 15px;
    }
}

/* Tablet and Small Desktop Styles */
@media (max-width: 992px) {
    .profile-container {
        max-width: 90%;
        margin: 32px auto;
        padding: 32px 24px;
    }

    .modal-content {
        max-width: 90%;
        margin: 20px;
        padding: 32px 24px;
    }

    .profile-details ul {
        gap: 8px 12px;
    }

    .profile-details ul li {
        font-size: 0.96rem;
        padding: 6px 14px;
    }
}

/* Tablet Styles */
@media (max-width: 768px) {
    body {
        padding: 8px;
    }

    .profile-container {
        margin: 16px auto;
        padding: 24px 20px;
        max-width: 95%;
    }

    .profile-details ul {
        gap: 8px 12px;
    }

    .profile-details ul li {
        font-size: 0.95rem;
        padding: 6px 14px;
    }

    .modal-content {
        max-width: 95%;
        margin: 10px;
        padding: 24px 20px;
    }

    .contact-section {
        margin-top: 24px;
        padding: 16px 0;
    }

    #contact-details {
        max-width: 90%;
        padding: 20px;
        margin: 20px auto;
    }
}

/* Extra Small Mobile Styles */
@media (max-width: 360px) {
    .profile-container {
        margin: 4px auto;
        padding: 12px 8px;
        border-radius: 8px;
    }

    .profile-photo {
        width: 80px;
        height: 80px;
    }

    #profile-name {
        font-size: 1.6rem;
    }

    .profile-details ul li {
        font-size: 0.85rem;
        padding: 4px 10px;
        border-radius: 12px;
    }

    .modal-content {
        padding: 16px 12px;
        border-radius: 12px;
    }

    #contact-details {
        padding: 12px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    body {
        padding: 4px;
    }

    .profile-container {
        margin: 8px auto;
        padding: 16px 12px;
        max-width: 98%;
        border-radius: 12px;
    }

    .profile-photo {
        width: 100px;
        height: 100px;
        margin-bottom: 12px;
    }

    #profile-name {
        font-size: 1.8rem;
        letter-spacing: 0.5px;
    }

    #profile-tagline {
        font-size: 1rem;
    }

    .profile-meta {
        font-size: 1rem;
    }

    .profile-details h2 {
        font-size: 1.1rem;
        margin-top: 20px;
        margin-bottom: 8px;
    }

    .profile-details p, .profile-details ul {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .profile-details ul {
        gap: 6px 8px;
    }

    .profile-details ul li {
        font-size: 0.9rem;
        padding: 5px 12px;
        border-radius: 14px;
    }

    #edit-profile-btn {
        padding: 10px 24px;
        font-size: 1rem;
        margin-top: 24px;
    }

    .modal-content {
        padding: 20px 16px;
        max-width: 98%;
        margin: 5px;
        border-radius: 16px;
    }

    .modal-content h2 {
        font-size: 1.3rem;
        margin-bottom: 16px;
    }

    .modal-content textarea {
        font-size: 0.95rem;
        padding: 8px;
    }

    .contact-section {
        margin-top: 20px;
        padding: 12px 0;
    }

    #contact-details {
        max-width: 95%;
        padding: 16px;
        margin: 16px auto;
        border-radius: 12px;
    }

    #contact-details h2 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }

    #contact-details p {
        font-size: 0.95rem;
        margin-bottom: 8px;
    }

    .footer {
        margin-top: 24px;
        padding: 16px 0;
    }

    .footer p {
        font-size: 0.85rem;
    }
}
