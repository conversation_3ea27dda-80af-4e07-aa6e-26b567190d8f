// Auto-size input and textarea fields in the edit profile modal
function autoSizeField(field) {
	if (field.tagName === 'TEXTAREA') {
		field.style.height = 'auto';
		field.style.height = (field.scrollHeight) + 'px';
	} else if (field.tagName === 'INPUT') {
		field.style.width = 'auto';
		field.style.width = (field.scrollWidth + 20) + 'px';
	}
}

document.addEventListener('DOMContentLoaded', function() {
	// ...existing code...
	// Auto-size for all relevant fields
	var fields = [
		'edit-name',
		'edit-tagline',
		'edit-objective',
		'edit-qualifications',
		'edit-skills',
		'edit-hobbies',
		'edit-growth',
		'edit-looking-for'
	];
	fields.forEach(function(id) {
		var el = document.getElementById(id);
		if (el) {
			autoSizeField(el);
			el.addEventListener('input', function() {
				autoSizeField(el);
			});
		}
	});
});
// Cloudflare Turnstile CAPTCHA integration for contact details
document.addEventListener('DOMContentLoaded', function() {
	var captchaForm = document.getElementById('captcha-form');
	if (captchaForm) {
		captchaForm.addEventListener('submit', function(e) {
			e.preventDefault();
			// For real security, verify the token server-side!
			document.getElementById('contact-details').style.display = 'block';
			captchaForm.style.display = 'none';
		});
	}
});
// Dating Resume Profile Interactivity
document.addEventListener('DOMContentLoaded', function() {
	const editBtn = document.getElementById('edit-profile-btn');
	const modal = document.getElementById('edit-profile-modal');
	const closeBtn = document.querySelector('.close-btn');
	const form = document.getElementById('edit-profile-form');

	// Open modal
	editBtn.addEventListener('click', function() {
		modal.classList.remove('hidden');
	});

	// Close modal
	closeBtn.addEventListener('click', function() {
		modal.classList.add('hidden');
	});

	// Save profile changes
	form.addEventListener('submit', function(e) {
		e.preventDefault();
		document.getElementById('profile-name').textContent = document.getElementById('edit-name').value;
		document.getElementById('profile-tagline').textContent = document.getElementById('edit-tagline').value;
		document.getElementById('profile-objective').textContent = document.getElementById('edit-objective').value;
		document.getElementById('profile-summary').textContent = document.getElementById('edit-summary').value;
		document.getElementById('profile-looking-for').textContent = document.getElementById('edit-looking-for').value;

		// Update Relationship Qualifications
		const qualifications = document.getElementById('edit-qualifications').value.split(',').map(i => i.trim()).filter(i => i);
		const qualificationsList = document.getElementById('profile-qualifications');
		qualificationsList.innerHTML = '';
		qualifications.forEach(function(q) {
			const li = document.createElement('li');
			li.textContent = q;
			qualificationsList.appendChild(li);
		});

		// Update Skills
		const skills = document.getElementById('edit-skills').value.split(',').map(i => i.trim()).filter(i => i);
		const skillsList = document.getElementById('profile-skills');
		skillsList.innerHTML = '';
		skills.forEach(function(skill) {
			const li = document.createElement('li');
			li.textContent = skill;
			skillsList.appendChild(li);
		});

		// Update Hobbies
		const hobbies = document.getElementById('edit-hobbies').value.split(',').map(i => i.trim()).filter(i => i);
		const hobbiesList = document.getElementById('profile-hobbies');
		hobbiesList.innerHTML = '';
		hobbies.forEach(function(hobby) {
			const li = document.createElement('li');
			li.textContent = hobby;
			hobbiesList.appendChild(li);
		});

		// Update Personal Growth
		const growth = document.getElementById('edit-growth').value.split(',').map(i => i.trim()).filter(i => i);
		const growthList = document.getElementById('profile-growth');
		growthList.innerHTML = '';
		growth.forEach(function(g) {
			const li = document.createElement('li');
			li.textContent = g;
			growthList.appendChild(li);
		});

		modal.classList.add('hidden');
	});
});