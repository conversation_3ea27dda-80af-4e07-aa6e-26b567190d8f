// Auto-size input and textarea fields in the edit profile modal
function autoSizeField(field) {
	if (field.tagName === 'TEXTAREA') {
		field.style.height = 'auto';
		field.style.height = (field.scrollHeight) + 'px';
	} else if (field.tagName === 'INPUT') {
		field.style.width = 'auto';
		field.style.width = (field.scrollWidth + 20) + 'px';
	}
}

document.addEventListener('DOMContentLoaded', function() {
	// ...existing code...
	// Auto-size for all relevant fields
	var fields = [
		'edit-name',
		'edit-tagline',
		'edit-objective',
		'edit-qualifications',
		'edit-skills',
		'edit-hobbies',
		'edit-growth',
		'edit-looking-for'
	];
	fields.forEach(function(id) {
		var el = document.getElementById(id);
		if (el) {
			autoSizeField(el);
			el.addEventListener('input', function() {
				autoSizeField(el);
			});
		}
	});
});
// Cloudflare Turnstile CAPTCHA integration for contact details
document.addEventListener('DOMContentLoaded', function() {
	var captchaForm = document.getElementById('captcha-form');
	if (captchaForm) {
		captchaForm.addEventListener('submit', function(e) {
			e.preventDefault();
			// For real security, verify the token server-side!
			document.getElementById('contact-details').style.display = 'block';
			captchaForm.style.display = 'none';
		});
	}
});
// Enhanced Dating Resume Profile Interactivity
document.addEventListener('DOMContentLoaded', function() {
	// Initialize all functionality
	initializeEditSystem();
	initializeFABSystem();
	initializeQuickActions();
	initializeKeyboardShortcuts();
	initializeSectionControls();
	initializeProgressiveDisclosure();
});

// Edit System
function initializeEditSystem() {
	const editBtn = document.getElementById('edit-profile-btn');
	const modal = document.getElementById('edit-profile-modal');
	const closeBtn = document.querySelector('.close-btn');
	const form = document.getElementById('edit-profile-form');

	// Open modal
	editBtn.addEventListener('click', function() {
		modal.classList.remove('hidden');
		document.body.classList.add('edit-mode');
	});

	// Close modal
	closeBtn.addEventListener('click', function() {
		modal.classList.add('hidden');
		document.body.classList.remove('edit-mode');
	});

	// Save profile changes
	form.addEventListener('submit', function(e) {
		e.preventDefault();
		saveProfileChanges();
		modal.classList.add('hidden');
		document.body.classList.remove('edit-mode');
	});
}

// FAB System
function initializeFABSystem() {
	const fabMain = document.getElementById('fab-main');
	const fabMenu = document.getElementById('fab-menu');
	const fabEditAll = document.getElementById('fab-edit-all');
	const fabAddSkill = document.getElementById('fab-add-skill');
	const fabAddHobby = document.getElementById('fab-add-hobby');
	const fabContact = document.getElementById('fab-contact');

	// Toggle FAB menu
	fabMain.addEventListener('click', function() {
		fabMain.classList.toggle('active');
		fabMenu.classList.toggle('active');
	});

	// FAB actions
	fabEditAll.addEventListener('click', function() {
		document.getElementById('edit-profile-btn').click();
		closeFABMenu();
	});

	fabAddSkill.addEventListener('click', function() {
		openQuickAdd('skills');
		closeFABMenu();
	});

	fabAddHobby.addEventListener('click', function() {
		openQuickAdd('hobbies');
		closeFABMenu();
	});

	fabContact.addEventListener('click', function() {
		document.getElementById('captcha-form').scrollIntoView({ behavior: 'smooth' });
		closeFABMenu();
	});

	// Close FAB menu when clicking outside
	document.addEventListener('click', function(e) {
		if (!e.target.closest('.fab-container')) {
			closeFABMenu();
		}
	});
}

function closeFABMenu() {
	document.getElementById('fab-main').classList.remove('active');
	document.getElementById('fab-menu').classList.remove('active');
}

// Quick Actions
function initializeQuickActions() {
	// Section edit buttons
	document.querySelectorAll('.btn-section-edit').forEach(btn => {
		btn.addEventListener('click', function() {
			const section = this.dataset.section;
			openSectionEdit(section);
		});
	});

	// Section add buttons
	document.querySelectorAll('.btn-section-add').forEach(btn => {
		btn.addEventListener('click', function() {
			const section = this.dataset.section;
			openQuickAdd(section);
		});
	});

	// Section collapse buttons
	document.querySelectorAll('.btn-section-collapse').forEach(btn => {
		btn.addEventListener('click', function() {
			const section = this.dataset.section;
			toggleSection(section);
		});
	});

	// Item edit buttons
	document.querySelectorAll('.btn-item-edit').forEach(btn => {
		btn.addEventListener('click', function() {
			const item = this.closest('.list-group-item');
			const section = this.closest('ul').id.replace('profile-', '');
			const index = Array.from(item.parentNode.children).indexOf(item);
			openQuickEdit(section, index, item.querySelector('.item-text').textContent);
		});
	});

	// Item remove buttons
	document.querySelectorAll('.btn-item-remove').forEach(btn => {
		btn.addEventListener('click', function() {
			if (confirm('Are you sure you want to remove this item?')) {
				this.closest('.list-group-item').remove();
			}
		});
	});
}

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
	document.addEventListener('keydown', function(e) {
		// Don't trigger shortcuts when typing in inputs
		if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
			return;
		}

		switch(e.key.toLowerCase()) {
			case 'e':
				if (!e.ctrlKey && !e.metaKey) {
					document.getElementById('edit-profile-btn').click();
					e.preventDefault();
				}
				break;
			case 's':
				if (!e.ctrlKey && !e.metaKey) {
					const saveBtn = document.querySelector('button[type="submit"]:not(.hidden)');
					if (saveBtn) saveBtn.click();
					e.preventDefault();
				}
				break;
			case 'escape':
				closeAllModals();
				e.preventDefault();
				break;
			case '?':
				toggleShortcutsModal();
				e.preventDefault();
				break;
			case 'f':
				if (!e.ctrlKey && !e.metaKey) {
					document.getElementById('fab-main').click();
					e.preventDefault();
				}
				break;
			case 'k':
				if (e.ctrlKey || e.metaKey) {
					openQuickAdd('skills');
					e.preventDefault();
				}
				break;
			case 'h':
				if (e.ctrlKey || e.metaKey) {
					openQuickAdd('hobbies');
					e.preventDefault();
				}
				break;
		}

		// Quick save with Ctrl+Enter
		if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
			const activeForm = document.querySelector('form:not(.hidden)');
			if (activeForm) {
				activeForm.dispatchEvent(new Event('submit'));
				e.preventDefault();
			}
		}
	});
}

// Section Controls
function initializeSectionControls() {
	// Initialize collapse states
	const collapsedSections = JSON.parse(localStorage.getItem('collapsedSections') || '[]');
	collapsedSections.forEach(sectionId => {
		const section = document.getElementById(`profile-${sectionId}`);
		if (section) {
			section.classList.add('collapsed');
			const btn = document.querySelector(`[data-section="${sectionId}"] .collapse-icon`);
			if (btn) btn.textContent = '🔼';
		}
	});
}

// Progressive Disclosure
function initializeProgressiveDisclosure() {
	// Show advanced controls on edit mode
	const observer = new MutationObserver(function(mutations) {
		mutations.forEach(function(mutation) {
			if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
				const editMode = document.body.classList.contains('edit-mode');
				document.querySelectorAll('.advanced-controls').forEach(control => {
					control.classList.toggle('visible', editMode);
				});
			}
		});
	});

	observer.observe(document.body, { attributes: true });
}

// Utility Functions
function openQuickAdd(section) {
	const modal = document.getElementById('quick-add-modal');
	const title = document.getElementById('quick-add-title');
	const input = document.getElementById('quick-add-input');
	const sectionInput = document.getElementById('quick-add-section');

	const sectionNames = {
		'skills': 'Skill',
		'hobbies': 'Hobby',
		'qualifications': 'Qualification',
		'growth': 'Growth Item'
	};

	title.textContent = `Add New ${sectionNames[section] || 'Item'}`;
	input.placeholder = `Enter new ${(sectionNames[section] || 'item').toLowerCase()}...`;
	sectionInput.value = section;
	input.value = '';

	modal.classList.remove('hidden');
	input.focus();
}

function openQuickEdit(section, index, currentText) {
	const modal = document.getElementById('quick-edit-modal');
	const title = document.getElementById('quick-edit-title');
	const input = document.getElementById('quick-edit-input');
	const sectionInput = document.getElementById('quick-edit-section');
	const indexInput = document.getElementById('quick-edit-index');

	title.textContent = 'Edit Item';
	input.value = currentText;
	sectionInput.value = section;
	indexInput.value = index;

	modal.classList.remove('hidden');
	input.focus();
	input.select();
}

function openSectionEdit(section) {
	// For now, open the main edit modal
	// In a full implementation, this would open section-specific modals
	document.getElementById('edit-profile-btn').click();
}

function toggleSection(section) {
	const sectionElement = document.getElementById(`profile-${section}`);
	const btn = document.querySelector(`[data-section="${section}"] .collapse-icon`);

	if (sectionElement && btn) {
		sectionElement.classList.toggle('collapsed');
		const isCollapsed = sectionElement.classList.contains('collapsed');
		btn.textContent = isCollapsed ? '🔼' : '🔽';

		// Save state
		const collapsedSections = JSON.parse(localStorage.getItem('collapsedSections') || '[]');
		if (isCollapsed) {
			if (!collapsedSections.includes(section)) {
				collapsedSections.push(section);
			}
		} else {
			const index = collapsedSections.indexOf(section);
			if (index > -1) {
				collapsedSections.splice(index, 1);
			}
		}
		localStorage.setItem('collapsedSections', JSON.stringify(collapsedSections));
	}
}

function closeAllModals() {
	document.querySelectorAll('.modal').forEach(modal => {
		modal.classList.add('hidden');
	});
	document.body.classList.remove('edit-mode');
	closeFABMenu();
}

function toggleShortcutsModal() {
	const modal = document.getElementById('shortcuts-modal');
	if (modal) {
		modal.classList.toggle('hidden');
	}
}

function saveProfileChanges() {
	// Update basic fields
	const nameField = document.getElementById('edit-name');
	const taglineField = document.getElementById('edit-tagline');
	const objectiveField = document.getElementById('edit-objective');
	const lookingForField = document.getElementById('edit-looking-for');

	if (nameField) document.getElementById('profile-name').textContent = nameField.value;
	if (taglineField) document.getElementById('profile-tagline').textContent = taglineField.value;
	if (objectiveField) document.getElementById('profile-objective').textContent = objectiveField.value;
	if (lookingForField) document.getElementById('profile-looking-for').textContent = lookingForField.value;

	// Update list sections
	updateListSection('qualifications');
	updateListSection('skills');
	updateListSection('hobbies');
	updateListSection('growth');
}

function updateListSection(section) {
	const editField = document.getElementById(`edit-${section}`);
	const displayList = document.getElementById(`profile-${section}`);

	if (editField && displayList) {
		const items = editField.value.split(',').map(i => i.trim()).filter(i => i);
		displayList.innerHTML = '';

		items.forEach(function(item) {
			const li = document.createElement('li');
			li.className = 'list-group-item';
			li.innerHTML = `
				<span class="item-text">${item}</span>
				<div class="item-actions">
					<button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
					<button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
				</div>
			`;
			displayList.appendChild(li);
		});

		// Re-attach event listeners
		initializeQuickActions();
	}
}

// Quick Add/Edit Form Handlers
document.addEventListener('DOMContentLoaded', function() {
	// Quick Add Form
	const quickAddForm = document.getElementById('quick-add-form');
	const quickAddClose = document.getElementById('quick-add-close');
	const quickAddCancel = document.getElementById('quick-add-cancel');

	if (quickAddForm) {
		quickAddForm.addEventListener('submit', function(e) {
			e.preventDefault();
			const section = document.getElementById('quick-add-section').value;
			const text = document.getElementById('quick-add-input').value.trim();

			if (text) {
				addItemToSection(section, text);
				document.getElementById('quick-add-modal').classList.add('hidden');
			}
		});
	}

	if (quickAddClose) {
		quickAddClose.addEventListener('click', function() {
			document.getElementById('quick-add-modal').classList.add('hidden');
		});
	}

	if (quickAddCancel) {
		quickAddCancel.addEventListener('click', function() {
			document.getElementById('quick-add-modal').classList.add('hidden');
		});
	}

	// Quick Edit Form
	const quickEditForm = document.getElementById('quick-edit-form');
	const quickEditClose = document.getElementById('quick-edit-close');
	const quickEditCancel = document.getElementById('quick-edit-cancel');

	if (quickEditForm) {
		quickEditForm.addEventListener('submit', function(e) {
			e.preventDefault();
			const section = document.getElementById('quick-edit-section').value;
			const index = parseInt(document.getElementById('quick-edit-index').value);
			const text = document.getElementById('quick-edit-input').value.trim();

			if (text) {
				updateItemInSection(section, index, text);
				document.getElementById('quick-edit-modal').classList.add('hidden');
			}
		});
	}

	if (quickEditClose) {
		quickEditClose.addEventListener('click', function() {
			document.getElementById('quick-edit-modal').classList.add('hidden');
		});
	}

	if (quickEditCancel) {
		quickEditCancel.addEventListener('click', function() {
			document.getElementById('quick-edit-modal').classList.add('hidden');
		});
	}

	// Shortcuts Modal
	const shortcutsClose = document.getElementById('shortcuts-close');
	if (shortcutsClose) {
		shortcutsClose.addEventListener('click', function() {
			document.getElementById('shortcuts-modal').classList.add('hidden');
		});
	}
});

function addItemToSection(section, text) {
	const list = document.getElementById(`profile-${section}`);
	if (list) {
		const li = document.createElement('li');
		li.className = 'list-group-item';
		li.innerHTML = `
			<span class="item-text">${text}</span>
			<div class="item-actions">
				<button class="btn-item-edit" title="Edit"><i class="edit-icon">✏️</i></button>
				<button class="btn-item-remove" title="Remove"><i class="remove-icon">❌</i></button>
			</div>
		`;
		list.appendChild(li);

		// Re-attach event listeners for the new item
		const editBtn = li.querySelector('.btn-item-edit');
		const removeBtn = li.querySelector('.btn-item-remove');

		editBtn.addEventListener('click', function() {
			const item = this.closest('.list-group-item');
			const section = this.closest('ul').id.replace('profile-', '');
			const index = Array.from(item.parentNode.children).indexOf(item);
			openQuickEdit(section, index, item.querySelector('.item-text').textContent);
		});

		removeBtn.addEventListener('click', function() {
			if (confirm('Are you sure you want to remove this item?')) {
				this.closest('.list-group-item').remove();
			}
		});
	}
}

function updateItemInSection(section, index, text) {
	const list = document.getElementById(`profile-${section}`);
	if (list && list.children[index]) {
		const item = list.children[index];
		const textSpan = item.querySelector('.item-text');
		if (textSpan) {
			textSpan.textContent = text;
		}
	}
}